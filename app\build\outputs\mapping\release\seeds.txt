tv.danmaku.ijk.media.player.MediaPlayerProxy$6
tv.danmaku.ijk.media.player.IjkTimedText
com.example.linkeye.R$color
tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener
tv.danmaku.ijk.media.player.misc.IMediaDataSource
com.example.linkeye.R$layout
com.example.linkeye.TvGroup
tv.danmaku.ijk.media.player.AndroidMediaPlayer
tv.danmaku.ijk.media.player.IjkMediaPlayer$DefaultMediaCodecSelector
com.example.linkeye.MainActivity
tv.danmaku.ijk.media.player.ISurfaceTextureHolder
com.example.linkeye.R$raw
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$3
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$Formatter
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$9
tv.danmaku.ijk.media.player.MediaPlayerProxy$2
tv.danmaku.ijk.media.player.pragma.Pragma
com.example.linkeye.BuildConfig
com.example.linkeye.PlayerTileView$1
tv.danmaku.ijk.media.player.ffmpeg.FFmpegApi
tv.danmaku.ijk.media.player.misc.IMediaFormat
tv.danmaku.ijk.media.player.IjkMediaPlayer$1
tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener
com.example.linkeye.MainActivity$1
com.example.linkeye.PlayerTileView
tv.danmaku.ijk.media.player.IjkMediaCodecInfo
tv.danmaku.ijk.media.player.MediaPlayerProxy$3
tv.danmaku.ijk.media.player.pragma.DebugLog
tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener
tv.danmaku.ijk.media.player.MediaPlayerProxy$1
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnControlMessageListener
tv.danmaku.ijk.media.player.misc.IjkTrackInfo
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$5
com.example.linkeye.PlayerTileView$2
com.example.linkeye.M3U8Parser$1
tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener
tv.danmaku.ijk.media.player.IjkMediaPlayer$EventHandler
tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener
tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener
tv.danmaku.ijk.media.player.misc.IAndroidIO
tv.danmaku.ijk.media.player.AndroidMediaPlayer$MediaDataSourceProxy
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnMediaCodecSelectListener
tv.danmaku.ijk.media.player.ISurfaceTextureHost
tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$1
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$6
tv.danmaku.ijk.media.player.exceptions.IjkMediaException
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$2
tv.danmaku.ijk.media.player.IjkMediaPlayer
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta
com.example.linkeye.CameraRepository
tv.danmaku.ijk.media.player.annotations.AccessedByNative
tv.danmaku.ijk.media.player.IjkLibLoader
com.example.linkeye.M3U8Parser$2
tv.danmaku.ijk.media.player.MediaPlayerProxy$5
com.example.linkeye.R$style
com.example.linkeye.R
com.example.linkeye.CameraInfo
tv.danmaku.ijk.media.player.IMediaPlayer
tv.danmaku.ijk.media.player.IMediaPlayer$OnTimedTextListener
tv.danmaku.ijk.media.player.annotations.CalledByNative
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo
tv.danmaku.ijk.media.player.MediaInfo
tv.danmaku.ijk.media.player.AbstractMediaPlayer
tv.danmaku.ijk.media.player.MediaPlayerProxy$8
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder
tv.danmaku.ijk.media.player.misc.IjkMediaFormat
com.example.linkeye.MainActivity$2
com.example.linkeye.M3U8Parser$M3U8LoadCallback
com.example.linkeye.R$id
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$8
com.example.linkeye.MainActivity$CameraAssignment
com.example.linkeye.R$xml
tv.danmaku.ijk.media.player.TextureMediaPlayer
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$4
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener
tv.danmaku.ijk.media.player.misc.AndroidMediaFormat
tv.danmaku.ijk.media.player.MediaPlayerProxy$7
com.example.linkeye.MainActivity$3
com.example.linkeye.M3U8Parser
tv.danmaku.ijk.media.player.IjkMediaMeta
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$7
com.example.linkeye.TvChannel
tv.danmaku.ijk.media.player.misc.ITrackInfo
tv.danmaku.ijk.media.player.MediaPlayerProxy
tv.danmaku.ijk.media.player.MediaPlayerProxy$4
com.example.linkeye.R$drawable
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_SET_VIDEO_SIZE
tv.danmaku.ijk.media.player.AndroidMediaPlayer: android.media.MediaDataSource mMediaDataSource
com.example.linkeye.MainActivity: java.lang.String KEY_TV_MODE
tv.danmaku.ijk.media.player.MediaPlayerProxy: tv.danmaku.ijk.media.player.IMediaPlayer mBackEndMediaPlayer
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_AUDIO_DECODER
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_FRONT_RIGHT
tv.danmaku.ijk.media.player.pragma.DebugLog: boolean ENABLE_WARN
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_AUDIO_SEEK_RENDERING_START
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_AUDIO_RENDERING_START
tv.danmaku.ijk.media.player.IjkMediaPlayer: tv.danmaku.ijk.media.player.IjkLibLoader sLocalLibLoader
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_TRAFFIC_STATISTIC_BYTE_COUNT
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: java.lang.String mMimeType
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_OPEN_INPUT
tv.danmaku.ijk.media.player.IjkMediaPlayer: int OPT_CATEGORY_PLAYER
tv.danmaku.ijk.media.player.IjkMediaMeta: java.util.ArrayList mStreams
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_STREAMS
com.example.linkeye.MainActivity: android.widget.GridLayout grid
tv.danmaku.ijk.media.player.exceptions.IjkMediaException: long serialVersionUID
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_VIDEO_DECODED_START
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_ASYNC_STATISTIC_BUF_CAPACITY
tv.danmaku.ijk.media.player.IjkMediaPlayer: int SDL_FCC_RV32
tv.danmaku.ijk.media.player.pragma.DebugLog: boolean ENABLE_DEBUG
com.example.linkeye.R$drawable: int ic_mute
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_BIT_RATE_UI
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_LATEST_SEEK_LOAD_DURATION
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_7POINT1_WIDE
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int EVENT_WILL_HTTP_SEEK
tv.danmaku.ijk.media.player.IjkTimedText: android.graphics.Rect mTextBounds
tv.danmaku.ijk.media.player.misc.ITrackInfo: int MEDIA_TRACK_TYPE_UNKNOWN
tv.danmaku.ijk.media.player.pragma.Pragma: boolean ENABLE_VERBOSE
tv.danmaku.ijk.media.player.AndroidMediaPlayer$MediaDataSourceProxy: tv.danmaku.ijk.media.player.misc.IMediaDataSource mMediaDataSource
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: tv.danmaku.ijk.media.player.AndroidMediaPlayer this$0
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mHeight
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_VIDEO_CACHED_BYTES
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_SAMPLE_RATE
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_VAL_TYPE__UNKNOWN
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_ERROR_MALFORMED
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mIndex
com.example.linkeye.TvGroup: java.util.List channels
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_QUAD
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: android.media.MediaPlayer$TrackInfo mTrackInfo
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_HIGH_444_INTRA
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_PORT
com.example.linkeye.M3U8Parser: java.lang.String PREFS_NAME
com.example.linkeye.M3U8Parser: java.util.regex.Pattern TVG_LOGO_PATTERN
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_TIMEDTEXT_STREAM
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_SET_VIDEO_SAR
tv.danmaku.ijk.media.player.IjkMediaPlayer: int mListenerContext
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String mCodecName
com.example.linkeye.MainActivity: int gridMode
tv.danmaku.ijk.media.player.IjkMediaMeta: long mBitrate
com.example.linkeye.M3U8Parser: java.util.regex.Pattern EXTINF_PATTERN
com.example.linkeye.MainActivity: boolean isTvMode
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_4POINT0
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_SAMPLE_RATE_UI
tv.danmaku.ijk.media.player.AbstractMediaPlayer: tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener mOnErrorListener
tv.danmaku.ijk.media.player.IjkMediaPlayer: boolean mScreenOnWhilePlaying
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_7POINT1
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_PLAYBACK_COMPLETE
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_VIDEO_SEEK_RENDERING_START
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_TOP_CENTER
com.example.linkeye.PlayerTileView: boolean isRetryingTvSources
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int RANK_LAST_CHANCE
com.example.linkeye.BuildConfig: java.lang.String BUILD_TYPE
tv.danmaku.ijk.media.player.IjkMediaMeta: long mStartUS
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_7POINT1_WIDE_BACK
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_ERROR_UNSUPPORTED
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_INTRA
com.example.linkeye.MainActivity: android.app.AlertDialog currentMenuDialog
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_2_2
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_VIDEO_TRACK_LAGGING
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_2_1
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_2POINT1
com.example.linkeye.CameraInfo: java.lang.String url
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_CODEC_PIXEL_FORMAT
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_5POINT0
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$1: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
tv.danmaku.ijk.media.player.AbstractMediaPlayer: tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener mOnSeekCompleteListener
tv.danmaku.ijk.media.player.MediaPlayerProxy$5: tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener val$finalListener
com.example.linkeye.M3U8Parser: java.util.regex.Pattern GROUP_TITLE_PATTERN
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_CAVLC_444
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_ERROR_IO
tv.danmaku.ijk.media.player.misc.ITrackInfo: int MEDIA_TRACK_TYPE_SUBTITLE
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_DURATION_US
com.example.linkeye.R$raw: int cameras
tv.danmaku.ijk.media.player.MediaInfo: java.lang.String mAudioDecoderImpl
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_FPS_DEN
com.example.linkeye.PlayerTileView: android.view.View focusBorder
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_UNKNOWN
com.example.linkeye.MainActivity$3: com.example.linkeye.MainActivity this$0
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mFpsDen
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_AUDIO_DECODED_START
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int RANK_NON_STANDARD
com.example.linkeye.MainActivity: java.util.List tvGroups
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mSarNum
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_FRAME_RATE_UI
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_VERBOSE
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.util.Map sFormatterMap
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_TOP_FRONT_RIGHT
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int EVENT_DID_HTTP_OPEN
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_7POINT0_FRONT
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mFpsNum
tv.danmaku.ijk.media.player.TextureMediaPlayer: tv.danmaku.ijk.media.player.ISurfaceTextureHost mSurfaceTextureHost
tv.danmaku.ijk.media.player.misc.ITrackInfo: int MEDIA_TRACK_TYPE_VIDEO
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$9: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_IP
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_FPS_NUM
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$4: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
com.example.linkeye.MainActivity: java.util.List tiles
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_TBR_DEN
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_4POINT1
com.example.linkeye.PlayerTileView: android.widget.ImageView muteBadge
tv.danmaku.ijk.media.player.MediaPlayerProxy$2: tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener val$finalListener
tv.danmaku.ijk.media.player.AndroidMediaPlayer: java.lang.Object mInitLock
com.example.linkeye.TvChannel: int currentSourceIndex
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_VIDEO_CACHED_DURATION
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int EVENT_DID_HTTP_SEEK
tv.danmaku.ijk.media.player.IjkMediaPlayer: boolean mStayAwake
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_SUBTITLE_TIMED_OUT
com.example.linkeye.PlayerTileView$1: com.example.linkeye.PlayerTileView this$0
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_AUDIO_CACHED_DURATION
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_VIDEO_CACHED_PACKETS
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int CTRL_WILL_HTTP_OPEN
tv.danmaku.ijk.media.player.pragma.DebugLog: boolean ENABLE_ERROR
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: android.media.MediaCodecInfo mCodecInfo
com.example.linkeye.R$xml: int network_security_config
tv.danmaku.ijk.media.player.MediaPlayerProxy$8: tv.danmaku.ijk.media.player.IMediaPlayer$OnTimedTextListener val$finalListener
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_STEREO_RIGHT
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_MAIN
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_BIT_RATE
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_BAD_INTERLEAVING
tv.danmaku.ijk.media.player.IjkMediaPlayer: int SDL_FCC_RV16
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_TOP_BACK_LEFT
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_TOP_FRONT_LEFT
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_STEREO_DOWNMIX
tv.danmaku.ijk.media.player.IjkMediaPlayer: int OPT_CATEGORY_CODEC
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_3POINT1
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int EVENT_WILL_HTTP_OPEN
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_METADATA_UPDATE
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_NETWORK_BANDWIDTH
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_BUFFERING_START
com.example.linkeye.R$layout: int player_tile
tv.danmaku.ijk.media.player.pragma.DebugLog: boolean ENABLE_VERBOSE
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_AUDIO_CACHED_BYTES
com.example.linkeye.MainActivity$2: com.example.linkeye.MainActivity this$0
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: int mTrackType
tv.danmaku.ijk.media.player.IjkMediaPlayer: boolean mIsNativeInitialized
com.example.linkeye.PlayerTileView: com.example.linkeye.CameraInfo camera
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta mStreamMeta
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mSampleRate
com.example.linkeye.BuildConfig: java.lang.String APPLICATION_ID
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_SAR_NUM
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_ERROR_SERVER_DIED
com.example.linkeye.PlayerTileView$2: com.example.linkeye.PlayerTileView this$0
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_CONSTRAINED
tv.danmaku.ijk.media.player.AbstractMediaPlayer: tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener mOnPreparedListener
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int RANK_SOFTWARE
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_TYPE
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_5POINT1_BACK
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_ASYNC_STATISTIC_BUF_FORWARDS
com.example.linkeye.R$id: int grid_container
tv.danmaku.ijk.media.player.IjkMediaPlayer: int mVideoWidth
com.example.linkeye.R$id: int surface_view
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_COMPONENT_OPEN
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_UNKNOWN
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String mFormat
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_SURROUND_DIRECT_LEFT
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_VAL_TYPE__AUDIO
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_HIGH_444_PREDICTIVE
tv.danmaku.ijk.media.player.MediaPlayerProxy$1: tv.danmaku.ijk.media.player.MediaPlayerProxy this$0
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_NOT_SEEKABLE
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_EXTENDED
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROPV_DECODER_VIDEOTOOLBOX
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_FILE_SIZE
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_5POINT0_BACK
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_6POINT1_BACK
tv.danmaku.ijk.media.player.MediaInfo: tv.danmaku.ijk.media.player.IjkMediaMeta mMeta
com.example.linkeye.MainActivity$CameraAssignment: boolean muted
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROPV_DECODER_MEDIACODEC
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String mDataSource
com.example.linkeye.CameraInfo: java.lang.String name
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_CODEC_PROFILE
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_FLOAT_DROP_FRAME_RATE
tv.danmaku.ijk.media.player.IjkMediaPlayer$EventHandler: java.lang.ref.WeakReference mWeakPlayer
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta mMediaFormat
tv.danmaku.ijk.media.player.IjkMediaMeta: long mDurationUS
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mWidth
com.example.linkeye.M3U8Parser: android.os.Handler mainHandler
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_HIGH
tv.danmaku.ijk.media.player.IjkMediaPlayer: boolean mIsLibLoaded
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String mCodecLongName
com.example.linkeye.R$layout: int activity_main
com.example.linkeye.R$style: int AppTheme
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_ERROR
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROPV_DECODER_AVCODEC
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_VAL_TYPE__TIMEDTEXT
com.example.linkeye.MainActivity$1: com.example.linkeye.MainActivity this$0
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_TIMED_TEXT_ERROR
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_VIDEO_STREAM
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_SELECTED_AUDIO_STREAM
com.example.linkeye.PlayerTileView: android.view.SurfaceHolder surfaceHolder
com.example.linkeye.PlayerTileView: int MAX_RETRY_COUNT
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_CODEC_PROFILE_LEVEL_UI
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_HEIGHT
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mSarDen
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int RANK_ACCEPTABLE
tv.danmaku.ijk.media.player.IjkMediaPlayer: int PROP_FLOAT_VIDEO_DECODE_FRAMES_PER_SECOND
com.example.linkeye.MainActivity: java.lang.String KEY_GRID
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_SURROUND
tv.danmaku.ijk.media.player.misc.ITrackInfo: int MEDIA_TRACK_TYPE_AUDIO
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_OCTAGONAL
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_AUDIO_STREAM
com.example.linkeye.R$id: int loading_indicator
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_HIGH_10
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$7: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_ERROR
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_FRONT_CENTER
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: java.lang.String TAG
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_FRONT_LEFT
com.example.linkeye.PlayerTileView: android.os.Handler focusHandler
tv.danmaku.ijk.media.player.IjkMediaPlayer: int OPT_CATEGORY_FORMAT
tv.danmaku.ijk.media.player.IjkMediaPlayer: int mVideoHeight
tv.danmaku.ijk.media.player.AndroidMediaPlayer: boolean mIsReleased
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int CTRL_WILL_LIVE_OPEN
tv.danmaku.ijk.media.player.IjkMediaPlayer: int mVideoSarDen
com.example.linkeye.MainActivity$3: int val$tileIndex
com.example.linkeye.M3U8Parser: java.lang.String DEFAULT_M3U8_URL
com.example.linkeye.TvChannel: java.lang.String groupTitle
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_ERROR_UNKNOWN
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK
tv.danmaku.ijk.media.player.MediaInfo: java.lang.String mVideoDecoderImpl
com.example.linkeye.TvChannel: java.lang.String tvgLogo
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: long mBitrate
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LOW_FREQUENCY
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_VIDEO_ROTATION_CHANGED
tv.danmaku.ijk.media.player.IjkMediaPlayer: long mNativeMediaDataSource
com.example.linkeye.R$color: int focus_border_main
com.example.linkeye.TvChannel: java.util.List sources
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String mType
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_WIDE_LEFT
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_SELECTED_VIDEO_STREAM
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_SIDE_RIGHT
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_BASELINE
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_ASYNC_STATISTIC_BUF_BACKWARDS
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_STEREO
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mTbrDen
tv.danmaku.ijk.media.player.IjkMediaPlayer: long mNativeMediaPlayer
com.example.linkeye.PlayerTileView: android.view.SurfaceView surfaceView
tv.danmaku.ijk.media.player.misc.IMediaFormat: java.lang.String KEY_MIME
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_LANGUAGE
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_STARTED_AS_NEXT
tv.danmaku.ijk.media.player.MediaPlayerProxy$7: tv.danmaku.ijk.media.player.MediaPlayerProxy this$0
tv.danmaku.ijk.media.player.IjkMediaPlayer: android.view.SurfaceHolder mSurfaceHolder
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_STEREO_LEFT
tv.danmaku.ijk.media.player.AbstractMediaPlayer: tv.danmaku.ijk.media.player.IMediaPlayer$OnTimedTextListener mOnTimedTextListener
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int CTRL_DID_TCP_OPEN
tv.danmaku.ijk.media.player.misc.ITrackInfo: int MEDIA_TRACK_TYPE_METADATA
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LOW_FREQUENCY_2
tv.danmaku.ijk.media.player.MediaPlayerProxy$8: tv.danmaku.ijk.media.player.MediaPlayerProxy this$0
com.example.linkeye.R$drawable: int focus_border_high_contrast
com.example.linkeye.R$id: int focus_border
tv.danmaku.ijk.media.player.AndroidMediaPlayer: tv.danmaku.ijk.media.player.MediaInfo sMediaInfo
tv.danmaku.ijk.media.player.IjkMediaPlayer: tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener mOnNativeInvokeListener
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_VAL_TYPE__VIDEO
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String mLanguage
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_FLOAT_PLAYBACK_RATE
tv.danmaku.ijk.media.player.MediaPlayerProxy$2: tv.danmaku.ijk.media.player.MediaPlayerProxy this$0
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_FRONT_LEFT_OF_CENTER
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_CACHE_STATISTIC_FILE_POS
com.example.linkeye.R$id: int mute_badge
tv.danmaku.ijk.media.player.MediaPlayerProxy$7: tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener val$finalListener
com.example.linkeye.PlayerTileView: int RETRY_DELAY_MS
com.example.linkeye.BuildConfig: java.lang.String VERSION_NAME
com.example.linkeye.M3U8Parser: java.util.concurrent.ExecutorService executor
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_HIGH_422_INTRA
tv.danmaku.ijk.media.player.IjkMediaPlayer: int mNativeSurfaceTexture
tv.danmaku.ijk.media.player.misc.ITrackInfo: int MEDIA_TRACK_TYPE_TIMEDTEXT
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_MEDIA_ACCURATE_SEEK_COMPLETE
com.example.linkeye.M3U8Parser: java.util.regex.Pattern TVG_ID_PATTERN
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_6POINT1_FRONT
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_FIND_STREAM_INFO
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_RESOLUTION_UI
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String mCodecProfile
com.example.linkeye.MainActivity: java.lang.String KEY_PAGE
tv.danmaku.ijk.media.player.MediaInfo: java.lang.String mMediaPlayerName
com.example.linkeye.R$color: int focus_border_glow
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$2: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
com.example.linkeye.MainActivity: int GRID_MODE_TV
com.example.linkeye.BuildConfig: int VERSION_CODE
com.example.linkeye.R$id: int tile_root
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_SILENT
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_HIGH_10_INTRA
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_INFO
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_TIMED_TEXT
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_CODEC_NAME_UI
tv.danmaku.ijk.media.player.IjkMediaPlayer: tv.danmaku.ijk.media.player.IjkMediaPlayer$OnMediaCodecSelectListener mOnMediaCodecSelectListener
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_BUFFERING_END
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROPV_DECODER_UNKNOWN
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_CHANNEL_LAYOUT
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_INFO
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_5POINT1
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_DEBUG
tv.danmaku.ijk.media.player.IjkMediaPlayer: int mVideoSarNum
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String CODEC_NAME_H264
tv.danmaku.ijk.media.player.IjkMediaPlayer: tv.danmaku.ijk.media.player.IjkMediaPlayer$OnControlMessageListener mOnControlMessageListener
com.example.linkeye.MainActivity: java.util.List cameras
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: java.util.Map sKnownCodecList
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_ERROR_TIMED_OUT
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_TCP_SPEED
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_BUFFERING_UPDATE
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_SURROUND_DIRECT_RIGHT
com.example.linkeye.PlayerTileView: android.widget.TextView titleView
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$8: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_7POINT0
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_CODEC_LONG_NAME_UI
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int RANK_TESTED
com.example.linkeye.MainActivity: java.lang.String PREFS
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_BITRATE
com.example.linkeye.TvChannel: java.lang.String tvgId
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_BACK_RIGHT
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String TAG
com.example.linkeye.PlayerTileView: int retryCount
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$6: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
tv.danmaku.ijk.media.player.IjkMediaPlayer: int SDL_FCC_YV12
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_FD
tv.danmaku.ijk.media.player.IjkMediaPlayer: long mNativeAndroidIO
tv.danmaku.ijk.media.player.MediaPlayerProxy$3: tv.danmaku.ijk.media.player.MediaPlayerProxy this$0
com.example.linkeye.MainActivity$CameraAssignment: com.example.linkeye.CameraInfo camera
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_AUDIO_CACHED_PACKETS
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_DEFAULT
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_SEGMENT_INDEX
tv.danmaku.ijk.media.player.IjkMediaPlayer: int OPT_CATEGORY_SWS
com.example.linkeye.MainActivity$CameraAssignment: int tileIndex
com.example.linkeye.PlayerTileView: boolean muted
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_WARN
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: long mChannelLayout
tv.danmaku.ijk.media.player.MediaInfo: java.lang.String mAudioDecoder
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_OFFSET
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_CODEC_PROFILE_ID
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_WIDE_RIGHT
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_RETRY_COUNTER
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int RANK_SECURE
tv.danmaku.ijk.media.player.IjkMediaMeta: tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta mAudioStream
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_TOP_BACK_CENTER
com.example.linkeye.R$drawable: int focus_border_enhanced
tv.danmaku.ijk.media.player.AndroidMediaPlayer: tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder mInternalListenerAdapter
tv.danmaku.ijk.media.player.IjkMediaPlayer$DefaultMediaCodecSelector: tv.danmaku.ijk.media.player.IjkMediaPlayer$DefaultMediaCodecSelector sInstance
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int RANK_MAX
tv.danmaku.ijk.media.player.IjkMediaPlayer: int IJK_LOG_FATAL
tv.danmaku.ijk.media.player.misc.IMediaFormat: java.lang.String KEY_HEIGHT
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int CTRL_WILL_TCP_OPEN
tv.danmaku.ijk.media.player.MediaPlayerProxy$4: tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener val$finalListener
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int mTbrNum
com.example.linkeye.MainActivity: java.lang.String KEY_CAMERA_ASSIGNMENTS
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_HIGH_422
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_HTTP_CODE
tv.danmaku.ijk.media.player.AbstractMediaPlayer: tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener mOnBufferingUpdateListener
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_UNSUPPORTED_SUBTITLE
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_PREPARED
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_SEEK_COMPLETE
tv.danmaku.ijk.media.player.IjkMediaPlayer: android.os.PowerManager$WakeLock mWakeLock
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_SIDE_LEFT
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_SELECTED_TIMEDTEXT_STREAM
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_BACK_CENTER
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: java.lang.ref.WeakReference mWeakMediaPlayer
com.example.linkeye.BuildConfig: boolean DEBUG
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_FAMILIY
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_WIDTH
tv.danmaku.ijk.media.player.IjkMediaPlayer: int PROP_FLOAT_VIDEO_OUTPUT_FRAMES_PER_SECOND
tv.danmaku.ijk.media.player.pragma.DebugLog: boolean ENABLE_INFO
tv.danmaku.ijk.media.player.misc.IMediaFormat: java.lang.String KEY_WIDTH
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_NOP
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_CHANNEL_UI
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_CACHE_STATISTIC_PHYSICAL_POS
tv.danmaku.ijk.media.player.AbstractMediaPlayer: tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener mOnCompletionListener
com.example.linkeye.MainActivity: int page
tv.danmaku.ijk.media.player.MediaPlayerProxy$5: tv.danmaku.ijk.media.player.MediaPlayerProxy this$0
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_BACK_LEFT
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int mRank
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_LOGICAL_FILE_SIZE
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: java.lang.String ARG_URL
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_6POINT1
tv.danmaku.ijk.media.player.TextureMediaPlayer: android.graphics.SurfaceTexture mSurfaceTexture
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$3: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
tv.danmaku.ijk.media.player.MediaPlayerProxy$6: tv.danmaku.ijk.media.player.MediaPlayerProxy this$0
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_CACHE_STATISTIC_FILE_FORWARDS
tv.danmaku.ijk.media.player.MediaInfo: java.lang.String mVideoDecoder
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_HEXAGONAL
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_MONO
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_HIGH_444
com.example.linkeye.M3U8Parser: java.lang.String KEY_M3U8_URL
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_TBR_NUM
tv.danmaku.ijk.media.player.IjkMediaMeta: android.os.Bundle mMediaMeta
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_TOP_FRONT_CENTER
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_CODEC_LONG_NAME
tv.danmaku.ijk.media.player.AbstractMediaPlayer: tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener mOnVideoSizeChangedListener
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String KEY_IJK_CODEC_PIXEL_FORMAT_UI
tv.danmaku.ijk.media.player.IjkTimedText: java.lang.String mTextChars
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_TOP_BACK_RIGHT
tv.danmaku.ijk.media.player.AndroidMediaPlayer: java.lang.String mDataSource
com.example.linkeye.R$id: int title
tv.danmaku.ijk.media.player.IjkMediaPlayer: int MEDIA_ERROR
tv.danmaku.ijk.media.player.IjkMediaPlayer: tv.danmaku.ijk.media.player.IjkMediaPlayer$EventHandler mEventHandler
tv.danmaku.ijk.media.player.MediaPlayerProxy$6: tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener val$finalListener
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_SAR_DEN
com.example.linkeye.PlayerTileView: tv.danmaku.ijk.media.player.IjkMediaPlayer player
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_SHARE_CACHE_DATA
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_6POINT0_FRONT
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$5: tv.danmaku.ijk.media.player.misc.IjkMediaFormat this$0
tv.danmaku.ijk.media.player.misc.AndroidMediaFormat: android.media.MediaFormat mMediaFormat
tv.danmaku.ijk.media.player.IjkMediaMeta: tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta mVideoStream
tv.danmaku.ijk.media.player.MediaPlayerProxy$3: tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener val$finalListener
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: int CTRL_WILL_CONCAT_RESOLVE_SEGMENT
tv.danmaku.ijk.media.player.IMediaPlayer: int MEDIA_INFO_VIDEO_RENDERING_START
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: int RANK_NO_SENSE
com.example.linkeye.R$color: int focus_border_high_contrast
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_IMMEDIATE_RECONNECT
com.example.linkeye.TvGroup: java.lang.String name
com.example.linkeye.CameraInfo: java.lang.String id
tv.danmaku.ijk.media.player.MediaPlayerProxy$1: tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener val$finalListener
com.example.linkeye.PlayerTileView: android.view.View loadingIndicator
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_VIDEO_DECODER
tv.danmaku.ijk.media.player.AbstractMediaPlayer: tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener mOnInfoListener
tv.danmaku.ijk.media.player.AndroidMediaPlayer: android.media.MediaPlayer mInternalMediaPlayer
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_FRONT_RIGHT_OF_CENTER
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_START_US
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_CODEC_LEVEL
tv.danmaku.ijk.media.player.IjkMediaMeta: long AV_CH_LAYOUT_6POINT0
tv.danmaku.ijk.media.player.IjkMediaPlayer: int FFP_PROP_INT64_CACHE_STATISTIC_COUNT_BYTES
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_CODEC_NAME
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: android.os.Bundle mMeta
tv.danmaku.ijk.media.player.MediaPlayerProxy$4: tv.danmaku.ijk.media.player.MediaPlayerProxy this$0
tv.danmaku.ijk.media.player.IjkMediaMeta: int FF_PROFILE_H264_CONSTRAINED_BASELINE
com.example.linkeye.R$color: int focus_border_medium
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String IJKM_KEY_FORMAT
com.example.linkeye.MainActivity: void showM3U8UrlDialog()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setDisplay(android.view.SurfaceHolder)
tv.danmaku.ijk.media.player.MediaPlayerProxy$3: MediaPlayerProxy$3(tv.danmaku.ijk.media.player.MediaPlayerProxy,tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int _getLoopCount()
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: boolean onInfo(android.media.MediaPlayer,int,int)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void prepareAsync()
tv.danmaku.ijk.media.player.IjkMediaPlayer: float getSpeed(float)
tv.danmaku.ijk.media.player.IMediaPlayer: long getDuration()
tv.danmaku.ijk.media.player.TextureMediaPlayer: void setDisplay(android.view.SurfaceHolder)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$2: IjkMediaFormat$2(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.IjkTimedText: android.graphics.Rect getBounds()
com.example.linkeye.MainActivity: void lambda$showChannelsInGroup$13(int,com.example.linkeye.TvGroup,android.content.DialogInterface,int)
com.example.linkeye.MainActivity: void lambda$showM3U8UrlDialog$2(android.widget.EditText,android.content.DialogInterface,int)
tv.danmaku.ijk.media.player.AndroidMediaPlayer$MediaDataSourceProxy: void close()
tv.danmaku.ijk.media.player.pragma.DebugLog: void i(java.lang.String,java.lang.String)
tv.danmaku.ijk.media.player.pragma.DebugLog: void e(java.lang.String,java.lang.String)
tv.danmaku.ijk.media.player.IMediaPlayer: long getCurrentPosition()
com.example.linkeye.MainActivity: void assignCamerasSequentially(java.util.List,int)
com.example.linkeye.MainActivity: void lambda$assignCamerasSequentially$8(java.util.List,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String onSelectCodec(java.lang.Object,java.lang.String,int,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setOption(int,java.lang.String,java.lang.String)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setLooping(boolean)
tv.danmaku.ijk.media.player.IjkMediaPlayer: IjkMediaPlayer(tv.danmaku.ijk.media.player.IjkLibLoader)
com.example.linkeye.MainActivity: void buildGrid()
tv.danmaku.ijk.media.player.IjkMediaPlayer: tv.danmaku.ijk.media.player.misc.ITrackInfo[] getTrackInfo()
com.example.linkeye.PlayerTileView: void access$200(com.example.linkeye.PlayerTileView)
com.example.linkeye.MainActivity: org.json.JSONObject loadCameraAssignments()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$2: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setScreenOnWhilePlaying(boolean)
tv.danmaku.ijk.media.player.IMediaPlayer: boolean isLooping()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: AbstractMediaPlayer()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void deselectTrack(int)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void notifyOnCompletion()
tv.danmaku.ijk.media.player.MediaPlayerProxy$8: void onTimedText(tv.danmaku.ijk.media.player.IMediaPlayer,tv.danmaku.ijk.media.player.IjkTimedText)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void resetListeners()
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: int getTrackType()
com.example.linkeye.MainActivity: void enableFullscreen()
tv.danmaku.ijk.media.player.misc.IMediaFormat: int getInteger(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer: boolean isPlayable()
com.example.linkeye.MainActivity: android.widget.GridLayout access$500(com.example.linkeye.MainActivity)
tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener: void onSeekComplete(tv.danmaku.ijk.media.player.IMediaPlayer)
tv.danmaku.ijk.media.player.pragma.DebugLog: DebugLog()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$Formatter: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.misc.AndroidMediaFormat: java.lang.String getString(java.lang.String)
com.example.linkeye.M3U8Parser$2: M3U8Parser$2()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: boolean isPlayable()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void httphookReconnect()
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getAsyncStatisticBufForwards()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setPropertyFloat(int,float)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void native_init()
tv.danmaku.ijk.media.player.IjkMediaMeta: long getLong(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setOnControlMessageListener(tv.danmaku.ijk.media.player.IjkMediaPlayer$OnControlMessageListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setVideoSurface(android.view.Surface)
tv.danmaku.ijk.media.player.MediaPlayerProxy$7: MediaPlayerProxy$7(tv.danmaku.ijk.media.player.MediaPlayerProxy,tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: int getVideoHeight()
tv.danmaku.ijk.media.player.IjkMediaPlayer: android.os.Bundle getMediaMeta()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setDataSource(android.content.Context,android.net.Uri,java.util.Map)
tv.danmaku.ijk.media.player.MediaPlayerProxy: int getAudioSessionId()
com.example.linkeye.MainActivity$CameraAssignment: MainActivity$CameraAssignment(int,com.example.linkeye.CameraInfo,boolean)
tv.danmaku.ijk.media.player.IjkMediaPlayer: float getVideoDecodeFramesPerSecond()
tv.danmaku.ijk.media.player.IjkMediaPlayer: float getDropFrameRate()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void notifyOnSeekComplete()
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getAudioCachedDuration()
com.example.linkeye.R$xml: R$xml()
tv.danmaku.ijk.media.player.pragma.DebugLog: void v(java.lang.String,java.lang.String,java.lang.Throwable)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setDataSource(java.lang.String,java.lang.String[],java.lang.String[])
tv.danmaku.ijk.media.player.IjkMediaPlayer: int getVideoSarNum()
com.example.linkeye.MainActivity$3: void lambda$onClick$0(int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String getDataSource()
com.example.linkeye.MainActivity: boolean getMuteState(org.json.JSONObject,int,boolean)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void resetListeners()
com.example.linkeye.MainActivity: void buildTvModeGrid()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void start()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$Formatter: IjkMediaFormat$Formatter()
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: void onCompletion(android.media.MediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setOnMediaCodecSelectListener(tv.danmaku.ijk.media.player.IjkMediaPlayer$OnMediaCodecSelectListener)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setWakeMode(android.content.Context,int)
tv.danmaku.ijk.media.player.pragma.DebugLog: void e(java.lang.String,java.lang.String,java.lang.Throwable)
com.example.linkeye.MainActivity$3: MainActivity$3(com.example.linkeye.MainActivity,int)
tv.danmaku.ijk.media.player.misc.ITrackInfo: java.lang.String getInfoInline()
tv.danmaku.ijk.media.player.ISurfaceTextureHolder: void setSurfaceTexture(android.graphics.SurfaceTexture)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setOnBufferingUpdateListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener)
tv.danmaku.ijk.media.player.IMediaPlayer: void setOnVideoSizeChangedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setOnSeekCompleteListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener)
tv.danmaku.ijk.media.player.IMediaPlayer: void setDataSource(tv.danmaku.ijk.media.player.misc.IMediaDataSource)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long access$000(tv.danmaku.ijk.media.player.IjkMediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _release()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setDisplay(android.view.SurfaceHolder)
com.example.linkeye.MainActivity: void onWindowFocusChanged(boolean)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setDisplay(android.view.SurfaceHolder)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void reset()
com.example.linkeye.PlayerTileView: void updateTitle()
tv.danmaku.ijk.media.player.IMediaPlayer: void setLooping(boolean)
tv.danmaku.ijk.media.player.ffmpeg.FFmpegApi: FFmpegApi()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$4: IjkMediaFormat$4(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.TextureMediaPlayer: TextureMediaPlayer(tv.danmaku.ijk.media.player.IMediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void stayAwake(boolean)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setOnSeekCompleteListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setOnCompletionListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener)
com.example.linkeye.MainActivity: org.json.JSONObject getCurrentLayoutAssignments(org.json.JSONObject)
tv.danmaku.ijk.media.player.IMediaPlayer: int getAudioSessionId()
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getAudioCachedBytes()
tv.danmaku.ijk.media.player.IjkMediaPlayer: boolean isPlaying()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setOnErrorListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void native_setLogLevel(int)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String getFpsInline()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setLogEnabled(boolean)
com.example.linkeye.PlayerTileView: void hideLoadingIndicator()
tv.danmaku.ijk.media.player.MediaPlayerProxy$2: MediaPlayerProxy$2(tv.danmaku.ijk.media.player.MediaPlayerProxy,tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void native_profileEnd()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void attachInternalListeners()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void prepareAsync()
tv.danmaku.ijk.media.player.MediaPlayerProxy$7: boolean onInfo(tv.danmaku.ijk.media.player.IMediaPlayer,int,int)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setOnInfoListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener)
com.example.linkeye.MainActivity: void setupNormalFocusNavigation()
com.example.linkeye.TvGroup: TvGroup(java.lang.String)
com.example.linkeye.TvChannel: boolean hasNextSource()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setDataSource(android.content.Context,android.net.Uri)
tv.danmaku.ijk.media.player.exceptions.IjkMediaException: IjkMediaException()
tv.danmaku.ijk.media.player.IMediaPlayer: int getVideoSarNum()
com.example.linkeye.MainActivity: void restoreState()
tv.danmaku.ijk.media.player.IMediaPlayer: void setSurface(android.view.Surface)
com.example.linkeye.PlayerTileView: void surfaceCreated(android.view.SurfaceHolder)
tv.danmaku.ijk.media.player.IMediaPlayer: void setAudioStreamType(int)
com.example.linkeye.R$layout: R$layout()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setOnErrorListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setDataSource(java.lang.String,java.util.Map)
com.example.linkeye.TvChannel: boolean isTvChannel()
com.example.linkeye.MainActivity: boolean access$100(com.example.linkeye.MainActivity)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: long getCurrentPosition()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setLooping(boolean)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: int getVideoWidth()
tv.danmaku.ijk.media.player.IMediaPlayer: int getVideoWidth()
tv.danmaku.ijk.media.player.IjkMediaPlayer: float _getPropertyFloat(int,float)
com.example.linkeye.MainActivity$2: void onError(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void loadLibrariesOnce(tv.danmaku.ijk.media.player.IjkLibLoader)
com.example.linkeye.MainActivity: void onStop()
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: IjkMediaMeta$IjkStreamMeta(int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getCacheStatisticFileForwards()
com.example.linkeye.PlayerTileView: void init(android.content.Context)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: android.media.MediaPlayer getInternalMediaPlayer()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setKeepInBackground(boolean)
com.example.linkeye.M3U8Parser: void loadM3U8Async(android.content.Context,com.example.linkeye.M3U8Parser$M3U8LoadCallback)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setDataSource(java.lang.String)
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: tv.danmaku.ijk.media.player.misc.IMediaFormat getFormat()
com.example.linkeye.MainActivity: void pagePrev()
tv.danmaku.ijk.media.player.IjkMediaPlayer: float getVideoOutputFramesPerSecond()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$Formatter: java.lang.String format(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int getVideoSarDen()
tv.danmaku.ijk.media.player.IjkMediaPlayer: int access$502(tv.danmaku.ijk.media.player.IjkMediaPlayer,int)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: int getInteger(java.lang.String)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: IjkMediaFormat(tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta)
tv.danmaku.ijk.media.player.IMediaPlayer: void setWakeMode(android.content.Context,int)
com.example.linkeye.TvChannel: int getSourceCount()
tv.danmaku.ijk.media.player.IMediaPlayer: boolean isPlaying()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void stop()
com.example.linkeye.MainActivity: boolean lambda$buildNormalGrid$4(int,android.view.View)
com.example.linkeye.MainActivity: void toggleMute(int)
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: tv.danmaku.ijk.media.player.misc.IMediaFormat getFormat()
com.example.linkeye.MainActivity: void lambda$setTileCamera$15(int)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: java.lang.String getDataSource()
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: void setTrackType(int)
tv.danmaku.ijk.media.player.TextureMediaPlayer: void setSurfaceTexture(android.graphics.SurfaceTexture)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getVideoCachedBytes()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setStreamSelected(int,boolean)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int access$402(tv.danmaku.ijk.media.player.IjkMediaPlayer,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void selectTrack(int)
tv.danmaku.ijk.media.player.pragma.DebugLog: void vfmt(java.lang.String,java.lang.String,java.lang.Object[])
tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener: void onPrepared(tv.danmaku.ijk.media.player.IMediaPlayer)
tv.danmaku.ijk.media.player.IMediaPlayer: boolean isPlayable()
tv.danmaku.ijk.media.player.AndroidMediaPlayer$MediaDataSourceProxy: AndroidMediaPlayer$MediaDataSourceProxy(tv.danmaku.ijk.media.player.misc.IMediaDataSource)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int getAudioSessionId()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setOnVideoSizeChangedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener)
com.example.linkeye.MainActivity: void assignInitialCameras()
tv.danmaku.ijk.media.player.ISurfaceTextureHost: void releaseSurfaceTexture(android.graphics.SurfaceTexture)
tv.danmaku.ijk.media.player.IMediaPlayer: void setDataSource(java.io.FileDescriptor)
com.example.linkeye.R$drawable: R$drawable()
tv.danmaku.ijk.media.player.TextureMediaPlayer: void releaseSurfaceTexture()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setOnTimedTextListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnTimedTextListener)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void stop()
com.example.linkeye.MainActivity: void lambda$showTileMenu$9(android.content.DialogInterface)
tv.danmaku.ijk.media.player.IMediaPlayer: tv.danmaku.ijk.media.player.MediaInfo getMediaInfo()
tv.danmaku.ijk.media.player.IjkMediaMeta: int getInt(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int access$300(tv.danmaku.ijk.media.player.IjkMediaPlayer)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: int getVideoSarDen()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setVolume(float,float)
com.example.linkeye.PlayerTileView: boolean access$102(com.example.linkeye.PlayerTileView,boolean)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: AndroidMediaPlayer()
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getAudioCachedPackets()
com.example.linkeye.MainActivity: void lambda$showM3U8UrlDialog$1(android.widget.EditText,android.content.DialogInterface,int)
tv.danmaku.ijk.media.player.MediaPlayerProxy: long getCurrentPosition()
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String _getColorFormatName(int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getBitRate()
com.example.linkeye.MainActivity: boolean lambda$buildTvModeGrid$7(android.view.View)
tv.danmaku.ijk.media.player.IjkMediaPlayer$1: IjkMediaPlayer$1()
tv.danmaku.ijk.media.player.IjkMediaPlayer: android.os.Bundle _getMediaMeta()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setVolume(float,float)
com.example.linkeye.MainActivity: void access$600(com.example.linkeye.MainActivity,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setAndroidIOCallback(tv.danmaku.ijk.media.player.misc.IAndroidIO)
tv.danmaku.ijk.media.player.pragma.DebugLog: void v(java.lang.String,java.lang.String)
com.example.linkeye.MainActivity: void lambda$showTvChannelPicker$12(int,android.content.DialogInterface,int)
com.example.linkeye.TvChannel: int getCurrentSourceIndex()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: boolean notifyOnInfo(int,int)
com.example.linkeye.MainActivity$3: void onClick(android.content.DialogInterface,int)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String getBitrateInline()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$5: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setDataSource(java.lang.String)
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: void setMediaMeta(tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setDataSource(android.content.Context,android.net.Uri)
com.example.linkeye.MainActivity: void lambda$buildGrid$3()
tv.danmaku.ijk.media.player.IjkMediaMeta: int getInt(java.lang.String,int)
com.example.linkeye.CameraInfo: CameraInfo(java.lang.String,java.lang.String,java.lang.String)
com.example.linkeye.MainActivity$1: void onSuccess(java.util.List)
tv.danmaku.ijk.media.player.misc.ITrackInfo: tv.danmaku.ijk.media.player.misc.IMediaFormat getFormat()
tv.danmaku.ijk.media.player.AndroidMediaPlayer$MediaDataSourceProxy: int readAt(long,byte[],int,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int access$500(tv.danmaku.ijk.media.player.IjkMediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setPropertyLong(int,long)
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: java.lang.String getInfoInline()
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String getChannelLayoutInline()
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: void onPrepared(android.media.MediaPlayer)
com.example.linkeye.PlayerTileView: void surfaceChanged(android.view.SurfaceHolder,int,int,int)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void notifyOnBufferingUpdate(int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void pause()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: int getVideoSarNum()
tv.danmaku.ijk.media.player.ISurfaceTextureHolder: android.graphics.SurfaceTexture getSurfaceTexture()
tv.danmaku.ijk.media.player.IjkMediaPlayer$EventHandler: IjkMediaPlayer$EventHandler(tv.danmaku.ijk.media.player.IjkMediaPlayer,android.os.Looper)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: tv.danmaku.ijk.media.player.MediaInfo getMediaInfo()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setLooping(boolean)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getDuration()
tv.danmaku.ijk.media.player.MediaPlayerProxy: java.lang.String getDataSource()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$8: IjkMediaFormat$8(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.misc.ITrackInfo: int getTrackType()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setLogEnabled(boolean)
tv.danmaku.ijk.media.player.IMediaPlayer$OnTimedTextListener: void onTimedText(tv.danmaku.ijk.media.player.IMediaPlayer,tv.danmaku.ijk.media.player.IjkTimedText)
com.example.linkeye.MainActivity: void access$700(com.example.linkeye.MainActivity,int)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void start()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$9: IjkMediaFormat$9(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.MediaInfo: MediaInfo()
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: java.lang.String getLevelName(int)
com.example.linkeye.M3U8Parser: java.util.List parseM3U8FromUrl(java.lang.String)
com.example.linkeye.MainActivity: boolean lambda$buildTvModeGrid$6(android.view.View)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setOption(int,java.lang.String,long)
com.example.linkeye.MainActivity: boolean onKeyDown(int,android.view.KeyEvent)
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String _getAudioCodecInfo()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setFrameAtTime(java.lang.String,long,long,int,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int getVideoHeight()
com.example.linkeye.PlayerTileView: void assignCamera(com.example.linkeye.CameraInfo)
tv.danmaku.ijk.media.player.IMediaPlayer: void pause()
tv.danmaku.ijk.media.player.IjkMediaPlayer: int getSelectedTrack(int)
com.example.linkeye.PlayerTileView: void surfaceDestroyed(android.view.SurfaceHolder)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setDataSource(java.io.FileDescriptor)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getCurrentPosition()
com.example.linkeye.MainActivity: int dpToPx(int)
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: tv.danmaku.ijk.media.player.misc.AndroidTrackInfo[] fromTrackInfo(android.media.MediaPlayer$TrackInfo[])
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getFileSize()
com.example.linkeye.M3U8Parser$M3U8LoadCallback: void onSuccess(java.util.List)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String getResolutionInline()
tv.danmaku.ijk.media.player.IMediaPlayer: void release()
com.example.linkeye.TvGroup: void addChannel(com.example.linkeye.TvChannel)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getAsyncStatisticBufBackwards()
com.example.linkeye.MainActivity: void assignInitialTvChannels()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: long getDuration()
tv.danmaku.ijk.media.player.MediaPlayerProxy: int getVideoSarDen()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _start()
com.example.linkeye.PlayerTileView: PlayerTileView(android.content.Context,android.util.AttributeSet,int)
tv.danmaku.ijk.media.player.IMediaPlayer: void setVolume(float,float)
tv.danmaku.ijk.media.player.IMediaPlayer: void seekTo(long)
com.example.linkeye.MainActivity: java.lang.String getCurrentLayoutKey()
com.example.linkeye.MainActivity: MainActivity()
com.example.linkeye.M3U8Parser: java.util.List parseM3U8(android.content.Context)
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String getDurationInline()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setDataSource(android.content.Context,android.net.Uri,java.util.Map)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setScreenOnWhilePlaying(boolean)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$7: IjkMediaFormat$7(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
com.example.linkeye.MainActivity: void setupTvModeFocusNavigation()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$4: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: java.lang.String toString()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void reset()
tv.danmaku.ijk.media.player.IjkMediaMeta: java.lang.String getString(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void native_message_loop(java.lang.Object)
tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener: void onVideoSizeChanged(tv.danmaku.ijk.media.player.IMediaPlayer,int,int,int,int)
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: AndroidTrackInfo(android.media.MediaPlayer$TrackInfo)
tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener: void onCompletion(tv.danmaku.ijk.media.player.IMediaPlayer)
tv.danmaku.ijk.media.player.misc.AndroidMediaFormat: java.lang.String toString()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setScreenOnWhilePlaying(boolean)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int getInt(java.lang.String,int)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String getCodecLongNameInline()
com.example.linkeye.PlayerTileView: void lambda$handlePlaybackError$1()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: tv.danmaku.ijk.media.player.misc.ITrackInfo[] getTrackInfo()
tv.danmaku.ijk.media.player.misc.IMediaDataSource: long getSize()
tv.danmaku.ijk.media.player.IjkLibLoader: void loadLibrary(java.lang.String)
tv.danmaku.ijk.media.player.IMediaPlayer: void setKeepInBackground(boolean)
com.example.linkeye.M3U8Parser$M3U8LoadCallback: void onError(java.lang.String)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$Formatter: IjkMediaFormat$Formatter(tv.danmaku.ijk.media.player.misc.IjkMediaFormat$1)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$6: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
com.example.linkeye.MainActivity: com.example.linkeye.CameraInfo findCameraById(java.lang.String)
tv.danmaku.ijk.media.player.MediaPlayerProxy$3: void onBufferingUpdate(tv.danmaku.ijk.media.player.IMediaPlayer,int)
com.example.linkeye.MainActivity: void showChannelsInGroup(int,com.example.linkeye.TvGroup)
tv.danmaku.ijk.media.player.MediaPlayerProxy$1: MediaPlayerProxy$1(tv.danmaku.ijk.media.player.MediaPlayerProxy,tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener)
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: int getTrackType()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setOnTimedTextListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnTimedTextListener)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat: java.lang.String getString(java.lang.String)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: boolean isPlaying()
tv.danmaku.ijk.media.player.MediaPlayerProxy: MediaPlayerProxy(tv.danmaku.ijk.media.player.IMediaPlayer)
com.example.linkeye.MainActivity: void lambda$setTileTvChannel$14(int)
tv.danmaku.ijk.media.player.misc.ITrackInfo: java.lang.String getLanguage()
com.example.linkeye.PlayerTileView: void showLoadingIndicator()
com.example.linkeye.MainActivity: void switchToNextChannel(int)
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: AndroidMediaPlayer$AndroidMediaPlayerListenerHolder(tv.danmaku.ijk.media.player.AndroidMediaPlayer,tv.danmaku.ijk.media.player.AndroidMediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int access$602(tv.danmaku.ijk.media.player.IjkMediaPlayer,int)
com.example.linkeye.M3U8Parser$1: M3U8Parser$1()
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: java.util.Map getKnownCodecList()
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String _getVideoCodecInfo()
com.example.linkeye.M3U8Parser$1: void checkClientTrusted(java.security.cert.X509Certificate[],java.lang.String)
tv.danmaku.ijk.media.player.pragma.DebugLog: void i(java.lang.String,java.lang.String,java.lang.Throwable)
tv.danmaku.ijk.media.player.MediaPlayerProxy$2: void onCompletion(tv.danmaku.ijk.media.player.IMediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int access$400(tv.danmaku.ijk.media.player.IjkMediaPlayer)
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: IjkTrackInfo(tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta)
tv.danmaku.ijk.media.player.pragma.DebugLog: void printCause(java.lang.Throwable)
tv.danmaku.ijk.media.player.pragma.DebugLog: void d(java.lang.String,java.lang.String)
com.example.linkeye.PlayerTileView: boolean access$500(com.example.linkeye.PlayerTileView)
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String getColorFormatName(int)
tv.danmaku.ijk.media.player.MediaPlayerProxy$6: boolean onError(tv.danmaku.ijk.media.player.IMediaPlayer,int,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener: boolean onNativeInvoke(int,android.os.Bundle)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void prepareAsync()
com.example.linkeye.MainActivity: void lambda$toggleMute$10(com.example.linkeye.PlayerTileView)
tv.danmaku.ijk.media.player.IMediaPlayer: void setDataSource(android.content.Context,android.net.Uri)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int getVideoDecoder()
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String getString(java.lang.String)
com.example.linkeye.TvChannel: TvChannel(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.List)
com.example.linkeye.PlayerTileView: void release()
tv.danmaku.ijk.media.player.IMediaPlayer: int getVideoSarDen()
com.example.linkeye.MainActivity$1: void onError(java.lang.String)
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: boolean onError(android.media.MediaPlayer,int,int)
com.example.linkeye.MainActivity: void pageNext()
com.example.linkeye.R$color: R$color()
com.example.linkeye.MainActivity$1: MainActivity$1(com.example.linkeye.MainActivity)
tv.danmaku.ijk.media.player.pragma.DebugLog: void dfmt(java.lang.String,java.lang.String,java.lang.Object[])
tv.danmaku.ijk.media.player.MediaPlayerProxy: boolean isLooping()
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: void onBufferingUpdate(android.media.MediaPlayer,int)
com.example.linkeye.M3U8Parser$2: boolean verify(java.lang.String,javax.net.ssl.SSLSession)
com.example.linkeye.PlayerTileView: boolean isMuted()
com.example.linkeye.PlayerTileView: int access$002(com.example.linkeye.PlayerTileView,int)
tv.danmaku.ijk.media.player.IjkTimedText: java.lang.String getText()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void pause()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setOnPreparedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: boolean isLooping()
tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener: boolean onInfo(tv.danmaku.ijk.media.player.IMediaPlayer,int,int)
tv.danmaku.ijk.media.player.IjkMediaMeta: IjkMediaMeta()
tv.danmaku.ijk.media.player.pragma.DebugLog: void d(java.lang.String,java.lang.String,java.lang.Throwable)
tv.danmaku.ijk.media.player.IjkMediaMeta: java.util.ArrayList getParcelableArrayList(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer$1: void loadLibrary(java.lang.String)
tv.danmaku.ijk.media.player.misc.IMediaDataSource: void close()
com.example.linkeye.MainActivity: void onCreate(android.os.Bundle)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getSeekLoadDuration()
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String getCodecShortNameInline()
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: void onSeekComplete(android.media.MediaPlayer)
com.example.linkeye.TvChannel: java.lang.String getCurrentSource()
com.example.linkeye.PlayerTileView: void lambda$handlePlaybackError$2()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void stop()
tv.danmaku.ijk.media.player.MediaPlayerProxy: int getVideoSarNum()
com.example.linkeye.PlayerTileView$1: PlayerTileView$1(com.example.linkeye.PlayerTileView)
com.example.linkeye.PlayerTileView: void restartIfReady()
com.example.linkeye.PlayerTileView: boolean onKeyDown(int,android.view.KeyEvent)
tv.danmaku.ijk.media.player.IMediaPlayer: void setDisplay(android.view.SurfaceHolder)
tv.danmaku.ijk.media.player.ffmpeg.FFmpegApi: java.lang.String av_base64_encode(byte[])
tv.danmaku.ijk.media.player.IjkMediaPlayer: long _getPropertyLong(int,long)
com.example.linkeye.PlayerTileView: PlayerTileView(android.content.Context)
com.example.linkeye.PlayerTileView: void access$400(com.example.linkeye.PlayerTileView)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getVideoCachedPackets()
tv.danmaku.ijk.media.player.TextureMediaPlayer: void setSurfaceTextureHost(tv.danmaku.ijk.media.player.ISurfaceTextureHost)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: long getLong(java.lang.String,long)
com.example.linkeye.M3U8Parser: void lambda$loadM3U8Async$0(com.example.linkeye.M3U8Parser$M3U8LoadCallback,java.util.List)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setDataSource(tv.danmaku.ijk.media.player.misc.IMediaDataSource)
com.example.linkeye.MainActivity$1: void lambda$onSuccess$0()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _reset()
tv.danmaku.ijk.media.player.pragma.DebugLog: void w(java.lang.String,java.lang.String,java.lang.Throwable)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$3: IjkMediaFormat$3(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.IMediaPlayer: void setScreenOnWhilePlaying(boolean)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: java.lang.String getSampleRateInline()
com.example.linkeye.CameraInfo: boolean isTvChannel()
com.example.linkeye.TvChannel: java.lang.String getNextSource()
tv.danmaku.ijk.media.player.IMediaPlayer: void setOnPreparedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener)
tv.danmaku.ijk.media.player.IMediaPlayer: void prepareAsync()
com.example.linkeye.PlayerTileView: void setMuted(boolean)
com.example.linkeye.MainActivity: void lambda$chooseGridMode$16(android.content.DialogInterface,int)
tv.danmaku.ijk.media.player.MediaPlayerProxy: int getVideoWidth()
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getCacheStatisticFilePos()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _stop()
tv.danmaku.ijk.media.player.IjkMediaPlayer: int access$302(tv.danmaku.ijk.media.player.IjkMediaPlayer,int)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setOnInfoListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener)
com.example.linkeye.MainActivity: java.util.List access$200(com.example.linkeye.MainActivity)
tv.danmaku.ijk.media.player.IMediaPlayer: void stop()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setKeepInBackground(boolean)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getTrafficStatisticByteCount()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setDataSource(java.io.FileDescriptor,long,long)
tv.danmaku.ijk.media.player.IMediaPlayer: void setOnCompletionListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener)
com.example.linkeye.MainActivity: boolean lambda$buildTvModeGrid$5(android.view.View)
tv.danmaku.ijk.media.player.misc.IAndroidIO: long seek(long,int)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setWakeMode(android.content.Context,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void native_setup(java.lang.Object)
com.example.linkeye.M3U8Parser$1: java.security.cert.X509Certificate[] getAcceptedIssuers()
tv.danmaku.ijk.media.player.annotations.CalledByNative: java.lang.String value()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$Formatter: java.lang.String getDefaultString()
tv.danmaku.ijk.media.player.IMediaPlayer: int getVideoHeight()
tv.danmaku.ijk.media.player.MediaPlayerProxy: long getDuration()
tv.danmaku.ijk.media.player.IjkMediaPlayer: java.lang.String access$100()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setSurface(android.view.Surface)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setDataSource(tv.danmaku.ijk.media.player.misc.IMediaDataSource)
com.example.linkeye.M3U8Parser: void disableSSLVerification(javax.net.ssl.HttpsURLConnection)
com.example.linkeye.PlayerTileView$1: void onPrepared(tv.danmaku.ijk.media.player.IMediaPlayer)
com.example.linkeye.MainActivity: void lambda$onCreate$0()
com.example.linkeye.MainActivity: void buildNormalGrid()
tv.danmaku.ijk.media.player.misc.IAndroidIO: int open(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer: tv.danmaku.ijk.media.player.MediaInfo getMediaInfo()
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: java.lang.String toString()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void updateSurfaceScreenOn()
tv.danmaku.ijk.media.player.IMediaPlayer: void setDataSource(java.lang.String)
com.example.linkeye.MainActivity: void setTileTvChannel(int,com.example.linkeye.TvChannel)
tv.danmaku.ijk.media.player.MediaPlayerProxy$4: void onSeekComplete(tv.danmaku.ijk.media.player.IMediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: long getLong(java.lang.String)
tv.danmaku.ijk.media.player.MediaPlayerProxy: int getVideoHeight()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void seekTo(long)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$5: IjkMediaFormat$5(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: tv.danmaku.ijk.media.player.IjkMediaCodecInfo setupCandidate(android.media.MediaCodecInfo,java.lang.String)
com.example.linkeye.PlayerTileView: void applyMute()
tv.danmaku.ijk.media.player.MediaPlayerProxy$6: MediaPlayerProxy$6(tv.danmaku.ijk.media.player.MediaPlayerProxy,tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener)
com.example.linkeye.MainActivity: void debugShowJsonStorage()
tv.danmaku.ijk.media.player.IjkTimedText: IjkTimedText(android.graphics.Rect,java.lang.String)
tv.danmaku.ijk.media.player.IMediaPlayer: java.lang.String getDataSource()
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getTcpSpeed()
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: java.lang.String getInfoInline()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setSurface(android.view.Surface)
com.example.linkeye.BuildConfig: BuildConfig()
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: java.lang.String getProfileLevelName(int,int)
com.example.linkeye.MainActivity$2: void lambda$onSuccess$0(java.util.List)
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: java.lang.String getLanguage()
com.example.linkeye.MainActivity: void showTvChannelPicker(int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void native_profileBegin(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setSurface(android.view.Surface)
tv.danmaku.ijk.media.player.IMediaPlayer: void setOnTimedTextListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnTimedTextListener)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$8: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
com.example.linkeye.MainActivity: void switchToPreviousChannel(int)
com.example.linkeye.MainActivity: com.example.linkeye.TvChannel findNextChannel(com.example.linkeye.CameraInfo)
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: void onTimedText(android.media.MediaPlayer,android.media.TimedText)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void start()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void seekTo(long)
tv.danmaku.ijk.media.player.IjkMediaPlayer$EventHandler: void handleMessage(android.os.Message)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$7: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
com.example.linkeye.MainActivity: void lambda$showCameraPicker$11(int,android.content.DialogInterface,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setOnNativeInvokeListener(tv.danmaku.ijk.media.player.IjkMediaPlayer$OnNativeInvokeListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getCacheStatisticCountBytes()
tv.danmaku.ijk.media.player.misc.AndroidTrackInfo: tv.danmaku.ijk.media.player.misc.AndroidTrackInfo[] fromMediaPlayer(android.media.MediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _prepareAsync()
tv.danmaku.ijk.media.player.misc.IjkTrackInfo: java.lang.String getLanguage()
tv.danmaku.ijk.media.player.pragma.DebugLog: void efmt(java.lang.String,java.lang.String,java.lang.Object[])
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getVideoCachedDuration()
com.example.linkeye.R$style: R$style()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void notifyOnPrepared()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void release()
com.example.linkeye.MainActivity: void setTileCamera(int,int)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void notifyOnVideoSizeChanged(int,int,int,int)
com.example.linkeye.MainActivity: void showTileMenu(int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setWakeMode(android.content.Context,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: IjkMediaPlayer()
tv.danmaku.ijk.media.player.MediaPlayerProxy: boolean isPlaying()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _pause()
tv.danmaku.ijk.media.player.IMediaPlayer: void setOnInfoListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnInfoListener)
com.example.linkeye.M3U8Parser: java.lang.String getM3U8Url(android.content.Context)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void access$200(tv.danmaku.ijk.media.player.IjkMediaPlayer,boolean)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setAudioStreamType(int)
tv.danmaku.ijk.media.player.IjkMediaMeta$IjkStreamMeta: int getInt(java.lang.String)
tv.danmaku.ijk.media.player.misc.IAndroidIO: int read(byte[],int)
com.example.linkeye.PlayerTileView: boolean handlePlaybackError()
tv.danmaku.ijk.media.player.misc.IAndroidIO: int close()
com.example.linkeye.CameraRepository: CameraRepository()
tv.danmaku.ijk.media.player.IMediaPlayer: tv.danmaku.ijk.media.player.misc.ITrackInfo[] getTrackInfo()
tv.danmaku.ijk.media.player.misc.AndroidMediaFormat: int getInteger(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setDataSource(android.content.Context,android.net.Uri,java.util.Map)
com.example.linkeye.TvGroup: int getChannelCount()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setDataSource(android.content.Context,android.net.Uri)
com.example.linkeye.MainActivity: java.lang.String getCameraAssignment(org.json.JSONObject,int)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void seekTo(long)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$6: IjkMediaFormat$6(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
com.example.linkeye.PlayerTileView: void access$300(com.example.linkeye.PlayerTileView)
com.example.linkeye.R$id: R$id()
com.example.linkeye.PlayerTileView: void lambda$init$0(android.view.View,boolean)
com.example.linkeye.MainActivity: void saveCameraSelection(int,java.lang.String,boolean)
tv.danmaku.ijk.media.player.pragma.DebugLog: void wfmt(java.lang.String,java.lang.String,java.lang.Object[])
tv.danmaku.ijk.media.player.MediaPlayerProxy: tv.danmaku.ijk.media.player.misc.ITrackInfo[] getTrackInfo()
tv.danmaku.ijk.media.player.MediaPlayerProxy: tv.danmaku.ijk.media.player.MediaInfo getMediaInfo()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setKeepInBackground(boolean)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setLogEnabled(boolean)
tv.danmaku.ijk.media.player.IjkMediaPlayer$DefaultMediaCodecSelector: java.lang.String onMediaCodecSelect(tv.danmaku.ijk.media.player.IMediaPlayer,java.lang.String,int,int)
tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener: boolean onError(tv.danmaku.ijk.media.player.IMediaPlayer,int,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getCacheStatisticPhysicalPos()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void notifyOnTimedText(tv.danmaku.ijk.media.player.IjkTimedText)
com.example.linkeye.MainActivity: void access$300(com.example.linkeye.MainActivity)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setOption(int,java.lang.String,java.lang.String)
tv.danmaku.ijk.media.player.IMediaPlayer: void reset()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void reset()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setCacheShare(int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void initPlayer(tv.danmaku.ijk.media.player.IjkLibLoader)
com.example.linkeye.MainActivity: void showCameraPicker(int)
com.example.linkeye.MainActivity$2: void onSuccess(java.util.List)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setOnPreparedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnPreparedListener)
com.example.linkeye.MainActivity: void chooseGridMode()
com.example.linkeye.R: R()
tv.danmaku.ijk.media.player.MediaPlayerProxy: tv.danmaku.ijk.media.player.IMediaPlayer getInternalMediaPlayer()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setAndroidIOCallback(tv.danmaku.ijk.media.player.misc.IAndroidIO)
com.example.linkeye.MainActivity$2: void lambda$onError$1(java.lang.String)
com.example.linkeye.MainActivity: android.app.AlertDialog access$402(com.example.linkeye.MainActivity,android.app.AlertDialog)
com.example.linkeye.MainActivity: void loadTvChannels()
com.example.linkeye.M3U8Parser: void lambda$loadM3U8Async$2(java.lang.String,com.example.linkeye.M3U8Parser$M3U8LoadCallback)
com.example.linkeye.M3U8Parser: void setM3U8Url(android.content.Context,java.lang.String)
com.example.linkeye.PlayerTileView$2: PlayerTileView$2(com.example.linkeye.PlayerTileView)
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$1: IjkMediaFormat$1(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
com.example.linkeye.M3U8Parser: M3U8Parser()
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setDataSource(tv.danmaku.ijk.media.player.misc.IMediaDataSource)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setAudioStreamType(int)
tv.danmaku.ijk.media.player.misc.IMediaDataSource: int readAt(long,byte[],int,int)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setOnCompletionListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnCompletionListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: long getAsyncStatisticBufCapacity()
com.example.linkeye.PlayerTileView: PlayerTileView(android.content.Context,android.util.AttributeSet)
tv.danmaku.ijk.media.player.IMediaPlayer: void setOnBufferingUpdateListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener)
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setDataSource(tv.danmaku.ijk.media.player.misc.IMediaDataSource)
tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener: void onBufferingUpdate(tv.danmaku.ijk.media.player.IMediaPlayer,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void finalize()
tv.danmaku.ijk.media.player.TextureMediaPlayer: void reset()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$3: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.IjkMediaPlayer: tv.danmaku.ijk.media.player.misc.IjkTrackInfo[] getTrackInfo()
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnMediaCodecSelectListener: java.lang.String onMediaCodecSelect(tv.danmaku.ijk.media.player.IMediaPlayer,java.lang.String,int,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setLoopCount(int)
com.example.linkeye.M3U8Parser: void lambda$loadM3U8Async$1(com.example.linkeye.M3U8Parser$M3U8LoadCallback,java.lang.Exception)
tv.danmaku.ijk.media.player.pragma.Pragma: Pragma()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setOption(int,java.lang.String,long)
com.example.linkeye.MainActivity: void saveBasicState()
com.example.linkeye.MainActivity: void setupFocusNavigation()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setAudioStreamType(int)
com.example.linkeye.MainActivity: void saveCameraAssignments(org.json.JSONObject)
tv.danmaku.ijk.media.player.TextureMediaPlayer: void setSurface(android.view.Surface)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: boolean isLooping()
tv.danmaku.ijk.media.player.AndroidMediaPlayer$MediaDataSourceProxy: long getSize()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void releaseMediaDataSource()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$1: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
tv.danmaku.ijk.media.player.IMediaPlayer: void setOnSeekCompleteListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer$DefaultMediaCodecSelector: IjkMediaPlayer$DefaultMediaCodecSelector()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: boolean notifyOnError(int,int)
tv.danmaku.ijk.media.player.pragma.DebugLog: void ifmt(java.lang.String,java.lang.String,java.lang.Object[])
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void release()
com.example.linkeye.R$raw: R$raw()
tv.danmaku.ijk.media.player.pragma.DebugLog: void w(java.lang.String,java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: IjkMediaCodecInfo()
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: void dumpProfileLevels(java.lang.String)
tv.danmaku.ijk.media.player.IMediaPlayer: void setLogEnabled(boolean)
com.example.linkeye.PlayerTileView$2: boolean onError(tv.danmaku.ijk.media.player.IMediaPlayer,int,int)
tv.danmaku.ijk.media.player.IjkMediaPlayer: int getVideoWidth()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setDataSource(java.io.FileDescriptor)
tv.danmaku.ijk.media.player.TextureMediaPlayer: void release()
tv.danmaku.ijk.media.player.IMediaPlayer: void setOnErrorListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnErrorListener)
tv.danmaku.ijk.media.player.IjkMediaPlayer: boolean onNativeInvoke(java.lang.Object,int,android.os.Bundle)
com.example.linkeye.PlayerTileView: void prepareAndStart()
tv.danmaku.ijk.media.player.AbstractMediaPlayer: void setOnBufferingUpdateListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnBufferingUpdateListener)
com.example.linkeye.MainActivity: com.example.linkeye.TvChannel findPreviousChannel(com.example.linkeye.CameraInfo)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void initNativeOnce()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setDataSource(java.lang.String)
tv.danmaku.ijk.media.player.pragma.DebugLog: void printStackTrace(java.lang.Throwable)
tv.danmaku.ijk.media.player.MediaPlayerProxy: boolean isPlayable()
com.example.linkeye.M3U8Parser$1: void checkServerTrusted(java.security.cert.X509Certificate[],java.lang.String)
com.example.linkeye.M3U8Parser: java.util.List parseChannelSources(java.lang.String)
tv.danmaku.ijk.media.player.IMediaPlayer: void setDataSource(android.content.Context,android.net.Uri,java.util.Map)
tv.danmaku.ijk.media.player.IjkMediaCodecInfo: java.lang.String getProfileName(int)
com.example.linkeye.PlayerTileView: com.example.linkeye.CameraInfo getAssignedCamera()
tv.danmaku.ijk.media.player.IjkMediaPlayer: int access$600(tv.danmaku.ijk.media.player.IjkMediaPlayer)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setDataSource(java.io.FileDescriptor)
com.example.linkeye.PlayerTileView: java.lang.String getCurrentUrl()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setSpeed(float)
tv.danmaku.ijk.media.player.MediaPlayerProxy$8: MediaPlayerProxy$8(tv.danmaku.ijk.media.player.MediaPlayerProxy,tv.danmaku.ijk.media.player.IMediaPlayer$OnTimedTextListener)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void setVolume(float,float)
tv.danmaku.ijk.media.player.IjkMediaPlayer: void postEventFromNative(java.lang.Object,int,int,int,java.lang.Object)
tv.danmaku.ijk.media.player.IjkMediaMeta: tv.danmaku.ijk.media.player.IjkMediaMeta parse(android.os.Bundle)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void setOnVideoSizeChangedListener(tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener)
tv.danmaku.ijk.media.player.MediaPlayerProxy$5: void onVideoSizeChanged(tv.danmaku.ijk.media.player.IMediaPlayer,int,int,int,int)
tv.danmaku.ijk.media.player.IjkMediaMeta: long getLong(java.lang.String,long)
tv.danmaku.ijk.media.player.MediaPlayerProxy$4: MediaPlayerProxy$4(tv.danmaku.ijk.media.player.MediaPlayerProxy,tv.danmaku.ijk.media.player.IMediaPlayer$OnSeekCompleteListener)
com.example.linkeye.MainActivity: void onDestroy()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void native_finalize()
tv.danmaku.ijk.media.player.ISurfaceTextureHolder: void setSurfaceTextureHost(tv.danmaku.ijk.media.player.ISurfaceTextureHost)
com.example.linkeye.MainActivity: java.util.List access$002(com.example.linkeye.MainActivity,java.util.List)
tv.danmaku.ijk.media.player.MediaPlayerProxy$1: void onPrepared(tv.danmaku.ijk.media.player.IMediaPlayer)
tv.danmaku.ijk.media.player.misc.AndroidMediaFormat: AndroidMediaFormat(android.media.MediaFormat)
tv.danmaku.ijk.media.player.misc.IMediaFormat: java.lang.String getString(java.lang.String)
com.example.linkeye.MainActivity: void testM3U8Connection(java.lang.String)
tv.danmaku.ijk.media.player.MediaPlayerProxy$5: MediaPlayerProxy$5(tv.danmaku.ijk.media.player.MediaPlayerProxy,tv.danmaku.ijk.media.player.IMediaPlayer$OnVideoSizeChangedListener)
tv.danmaku.ijk.media.player.TextureMediaPlayer: android.graphics.SurfaceTexture getSurfaceTexture()
com.example.linkeye.TvChannel: void resetSourceIndex()
tv.danmaku.ijk.media.player.misc.IjkMediaFormat$9: java.lang.String doFormat(tv.danmaku.ijk.media.player.misc.IjkMediaFormat)
com.example.linkeye.CameraRepository: java.util.List load(android.content.Context)
tv.danmaku.ijk.media.player.MediaPlayerProxy: void release()
com.example.linkeye.TvGroup: boolean isEmpty()
tv.danmaku.ijk.media.player.AndroidMediaPlayer: int getAudioSessionId()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void _setDataSourceFd(int)
tv.danmaku.ijk.media.player.AndroidMediaPlayer$AndroidMediaPlayerListenerHolder: void onVideoSizeChanged(android.media.MediaPlayer,int,int)
com.example.linkeye.MainActivity$2: MainActivity$2(com.example.linkeye.MainActivity)
tv.danmaku.ijk.media.player.IMediaPlayer: void start()
tv.danmaku.ijk.media.player.IjkMediaPlayer: void setDataSource(tv.danmaku.ijk.media.player.misc.IMediaDataSource)
com.example.linkeye.MainActivity$1: void lambda$onError$1(java.lang.String)
tv.danmaku.ijk.media.player.IjkMediaPlayer$OnControlMessageListener: java.lang.String onControlResolveSegmentUrl(int)
tv.danmaku.ijk.media.player.AndroidMediaPlayer: void pause()
